// qaudiooutput.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioOutput : QObject
{
%TypeHeaderCode
#include <qaudiooutput.h>
%End

public:
    QAudioOutput(const QAudioDevice &device, QObject *parent /TransferThis/ = 0);
    explicit QAudioOutput(QObject *parent /TransferThis/ = 0);
    virtual ~QAudioOutput();
    float volume() const;
    QAudioDevice device() const;
    bool isMuted() const;

public slots:
    void setDevice(const QAudioDevice &device);
    void setVolume(float volume);
    void setMuted(bool muted);

signals:
    void deviceChanged();
    void volumeChanged(float volume);
    void mutedChanged(bool muted);
};

%End
