// qjsonvalue.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QJsonValue /AllowNone,TypeHintIn="Union[QJsonValue, QJsonValue.Type, QJsonArray, QJsonObject, bool, int, float, None, QString]"/
{
%TypeHeaderCode
#include <qjsonvalue.h>
%End

%ConvertToTypeCode
if (!sipIsErr)
    return qpycore_canConvertTo_QJsonValue(sipPy);

return qpycore_convertTo_QJsonValue(sipPy, sipTransferObj, sipCppPtr, sipIsErr);
%End

public:
    enum Type
    {
        Null,
        Bool,
        Double,
        String,
        Array,
        Object,
        Undefined,
    };

    QJsonValue(QJsonValue::Type type /Constrained/ = QJsonValue::Null);
    QJsonValue(const QJsonValue &other);
    ~QJsonValue();
    static QJsonValue fromVariant(const QVariant &variant);
    QVariant toVariant() const;
    QJsonValue::Type type() const;
    bool isNull() const;
    bool isBool() const;
    bool isDouble() const;
    bool isString() const;
    bool isArray() const;
    bool isObject() const;
    bool isUndefined() const;
    bool toBool(bool defaultValue = false) const;
    int toInt(int defaultValue = 0) const;
    qint64 toInteger(qint64 defaultValue = 0) const;
    double toDouble(double defaultValue = 0) const;
    QJsonArray toArray() const;
    QJsonArray toArray(const QJsonArray &defaultValue) const;
    QJsonObject toObject() const;
    QJsonObject toObject(const QJsonObject &defaultValue) const;
    bool operator==(const QJsonValue &other) const;
    bool operator!=(const QJsonValue &other) const;
    QString toString() const;
    QString toString(const QString &defaultValue) const;
    void swap(QJsonValue &other /Constrained/);
    const QJsonValue operator[](qsizetype i) const;
    const QJsonValue operator[](const QString &key) const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

QDataStream &operator<<(QDataStream &, const QJsonValue & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QJsonValue & /Constrained/) /ReleaseGIL/;
