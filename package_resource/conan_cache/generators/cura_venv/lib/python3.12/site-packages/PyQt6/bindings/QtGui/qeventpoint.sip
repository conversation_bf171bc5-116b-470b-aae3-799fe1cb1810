// qeventpoint.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QEventPoint
{
%TypeHeaderCode
#include <qeventpoint.h>
%End

public:
    enum State /BaseType=Flag/
    {
        Unknown,
        Stationary,
        Pressed,
        Updated,
        Released,
    };

    typedef QFlags<QEventPoint::State> States;
    QEventPoint(int pointId, QEventPoint::State state, const QPointF &scenePosition, const QPointF &globalPosition);
    QEventPoint(const QEventPoint &other);
    bool operator==(const QEventPoint &other) const;
    bool operator!=(const QEventPoint &other) const;
    ~QEventPoint();
    void swap(QEventPoint &other /Constrained/);
    QPointF position() const;
    QPointF pressPosition() const;
    QPointF grabPosition() const;
    QPointF lastPosition() const;
    QPointF scenePosition() const;
    QPointF scenePressPosition() const;
    QPointF sceneGrabPosition() const;
    QPointF sceneLastPosition() const;
    QPointF globalPosition() const;
    QPointF globalPressPosition() const;
    QPointF globalGrabPosition() const;
    QPointF globalLastPosition() const;
    QPointF normalizedPosition() const;
    QVector2D velocity() const;
    QEventPoint::State state() const;
    const QPointingDevice *device() const;
    int id() const;
    QPointingDeviceUniqueId uniqueId() const;
    ulong timestamp() const;
    ulong lastTimestamp() const;
    ulong pressTimestamp() const;
    qreal timeHeld() const;
    qreal pressure() const;
    qreal rotation() const;
    QSizeF ellipseDiameters() const;
    bool isAccepted() const;
    void setAccepted(bool accepted = true);
};
