// qlistview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QListView : QAbstractItemView
{
%TypeHeaderCode
#include <qlistview.h>
%End

public:
    enum Movement
    {
        Static,
        Free,
        Snap,
    };

    enum Flow
    {
        LeftToRight,
        TopToBottom,
    };

    enum ResizeMode
    {
        Fixed,
        Adjust,
    };

    enum LayoutMode
    {
        SinglePass,
        Batched,
    };

    enum ViewMode
    {
        ListMode,
        IconMode,
    };

    explicit QListView(QWidget *parent /TransferThis/ = 0);
    virtual ~QListView();
    void setMovement(QListView::Movement movement);
    QListView::Movement movement() const;
    void setFlow(QListView::Flow flow);
    QListView::Flow flow() const;
    void setWrapping(bool enable);
    bool isWrapping() const;
    void setResizeMode(QListView::ResizeMode mode);
    QListView::ResizeMode resizeMode() const;
    void setLayoutMode(QListView::LayoutMode mode);
    QListView::LayoutMode layoutMode() const;
    void setSpacing(int space);
    int spacing() const;
    void setGridSize(const QSize &size);
    QSize gridSize() const;
    void setViewMode(QListView::ViewMode mode);
    QListView::ViewMode viewMode() const;
    void clearPropertyFlags();
    bool isRowHidden(int row) const;
    void setRowHidden(int row, bool hide);
    void setModelColumn(int column);
    int modelColumn() const;
    void setUniformItemSizes(bool enable);
    bool uniformItemSizes() const;
    virtual QRect visualRect(const QModelIndex &index) const;
    virtual void scrollTo(const QModelIndex &index, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);
    virtual QModelIndex indexAt(const QPoint &p) const;
    virtual void reset();
    virtual void setRootIndex(const QModelIndex &index);

signals:
    void indexesMoved(const QModelIndexList &indexes);

protected:
    virtual void scrollContentsBy(int dx, int dy);
    virtual void dataChanged(const QModelIndex &topLeft, const QModelIndex &bottomRight, const QList<int> &roles = QList<int>());
    virtual void rowsInserted(const QModelIndex &parent, int start, int end);
    virtual void rowsAboutToBeRemoved(const QModelIndex &parent, int start, int end);
    virtual bool event(QEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void timerEvent(QTimerEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dropEvent(QDropEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void startDrag(Qt::DropActions supportedActions);
    virtual void paintEvent(QPaintEvent *e);
    virtual int horizontalOffset() const;
    virtual int verticalOffset() const;
    virtual QModelIndex moveCursor(QAbstractItemView::CursorAction cursorAction, Qt::KeyboardModifiers modifiers);
    QRect rectForIndex(const QModelIndex &index) const;
    void setPositionForIndex(const QPoint &position, const QModelIndex &index);
    virtual void setSelection(const QRect &rect, QItemSelectionModel::SelectionFlags command);
    virtual QRegion visualRegionForSelection(const QItemSelection &selection) const;
    virtual QModelIndexList selectedIndexes() const;
    virtual void updateGeometries();
    virtual bool isIndexHidden(const QModelIndex &index) const;
    virtual QSize viewportSizeHint() const;

public:
    void setBatchSize(int batchSize);
    int batchSize() const;
    void setWordWrap(bool on);
    bool wordWrap() const;
    void setSelectionRectVisible(bool show);
    bool isSelectionRectVisible() const;

protected:
    virtual void selectionChanged(const QItemSelection &selected, const QItemSelection &deselected);
    virtual void currentChanged(const QModelIndex &current, const QModelIndex &previous);
    virtual void initViewItemOption(QStyleOptionViewItem *option) const;

public:
    void setItemAlignment(Qt::Alignment alignment);
    Qt::Alignment itemAlignment() const;
};
