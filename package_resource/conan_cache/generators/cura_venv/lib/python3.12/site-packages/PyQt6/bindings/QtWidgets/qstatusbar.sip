// qstatusbar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStatusBar : QWidget
{
%TypeHeaderCode
#include <qstatusbar.h>
%End

public:
    explicit QStatusBar(QWidget *parent /TransferThis/ = 0);
    virtual ~QStatusBar();
    void addWidget(QWidget *widget /Transfer/, int stretch = 0);
    void addPermanentWidget(QWidget *widget /Transfer/, int stretch = 0);
    void removeWidget(QWidget *widget);
    void setSizeGripEnabled(bool);
    bool isSizeGripEnabled() const;
    QString currentMessage() const;
    int insertWidget(int index, QWidget *widget /Transfer/, int stretch = 0);
    int insertPermanentWidget(int index, QWidget *widget /Transfer/, int stretch = 0);

public slots:
    void showMessage(const QString &message, int msecs = 0);
    void clearMessage();

signals:
    void messageChanged(const QString &text);

protected:
    virtual void paintEvent(QPaintEvent *);
    virtual void resizeEvent(QResizeEvent *);
    void reformat();
    void hideOrShow();
    virtual bool event(QEvent *);
    virtual void showEvent(QShowEvent *);
};
