// qopenglpixeltransferoptions.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLPixelTransferOptions
{
%TypeHeaderCode
#include <qopenglpixeltransferoptions.h>
%End

public:
    QOpenGLPixelTransferOptions();
    QOpenGLPixelTransferOptions(const QOpenGLPixelTransferOptions &);
    ~QOpenGLPixelTransferOptions();
    void swap(QOpenGLPixelTransferOptions &other /Constrained/);
    void setAlignment(int alignment);
    int alignment() const;
    void setSkipImages(int skipImages);
    int skipImages() const;
    void setSkipRows(int skipRows);
    int skipRows() const;
    void setSkipPixels(int skipPixels);
    int skipPixels() const;
    void setImageHeight(int imageHeight);
    int imageHeight() const;
    void setRowLength(int rowLength);
    int rowLength() const;
    void setLeastSignificantByteFirst(bool lsbFirst);
    bool isLeastSignificantBitFirst() const;
    void setSwapBytesEnabled(bool swapBytes);
    bool isSwapBytesEnabled() const;
};
