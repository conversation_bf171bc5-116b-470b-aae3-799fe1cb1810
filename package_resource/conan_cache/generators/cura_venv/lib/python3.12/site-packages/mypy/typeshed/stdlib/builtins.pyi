import sys
import types
from _ast import AST
from _collections_abc import dict_items, dict_keys, dict_values
from _typeshed import (
    OpenBinaryMode,
    OpenBinaryModeReading,
    OpenBinaryModeUpdating,
    OpenBinaryModeWriting,
    OpenTextMode,
    ReadableBuffer,
    Self,
    StrOrBytesPath,
    SupportsAnext,
    SupportsDivMod,
    SupportsKeysAndGetItem,
    SupportsLenAndGetItem,
    SupportsNext,
    SupportsRDivMod,
    SupportsRichComparison,
    SupportsRichComparisonT,
    SupportsTrunc,
    SupportsWrite,
)
from io import BufferedRandom, BufferedReader, BufferedWriter, FileIO, TextIOWrapper
from types import CodeType, TracebackType
from typing import (
    IO,
    AbstractSet,
    Any,
    AsyncIterable,
    AsyncIterator,
    BinaryIO,
    ByteString,
    Callable,
    Generic,
    Iterable,
    Iterator,
    Mapping,
    MutableMapping,
    MutableSequence,
    MutableSet,
    NoReturn,
    Protocol,
    Reversible,
    Sequence,
    Sized,
    SupportsAbs,
    SupportsBytes,
    SupportsComplex,
    SupportsFloat,
    SupportsInt,
    SupportsRound,
    Tuple,
    Type,
    TypeVar,
    Union,
    overload,
)
from typing_extensions import Literal, SupportsIndex, TypeGuard, final

if sys.version_info >= (3, 9):
    from types import GenericAlias

_T = TypeVar("_T")
_T_co = TypeVar("_T_co", covariant=True)
_T_contra = TypeVar("_T_contra", contravariant=True)
_KT = TypeVar("_KT")
_VT = TypeVar("_VT")
_S = TypeVar("_S")
_T1 = TypeVar("_T1")
_T2 = TypeVar("_T2")
_T3 = TypeVar("_T3")
_T4 = TypeVar("_T4")
_T5 = TypeVar("_T5")
_TT = TypeVar("_TT", bound="type")
_TBE = TypeVar("_TBE", bound="BaseException")
_R = TypeVar("_R")  # Return-type TypeVar

class object:
    __doc__: str | None
    __dict__: dict[str, Any]
    __slots__: str | Iterable[str]
    __module__: str
    __annotations__: dict[str, Any]
    @property
    def __class__(self: _T) -> Type[_T]: ...
    # Ignore errors about type mismatch between property getter and setter
    @__class__.setter
    def __class__(self, __type: Type[object]) -> None: ...  # type: ignore # noqa: F811
    def __init__(self) -> None: ...
    def __new__(cls: Type[_T]) -> _T: ...
    def __setattr__(self, __name: str, __value: Any) -> None: ...
    def __eq__(self, __o: object) -> bool: ...
    def __ne__(self, __o: object) -> bool: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __hash__(self) -> int: ...
    def __format__(self, __format_spec: str) -> str: ...
    def __getattribute__(self, __name: str) -> Any: ...
    def __delattr__(self, __name: str) -> None: ...
    def __sizeof__(self) -> int: ...
    # return type of pickle methods is rather hard to express in the current type system
    # see #6661 and https://docs.python.org/3/library/pickle.html#object.__reduce__
    def __reduce__(self) -> str | Tuple[Any, ...]: ...
    if sys.version_info >= (3, 8):
        def __reduce_ex__(self, __protocol: SupportsIndex) -> str | Tuple[Any, ...]: ...
    else:
        def __reduce_ex__(self, __protocol: int) -> str | Tuple[Any, ...]: ...
    def __dir__(self) -> Iterable[str]: ...
    def __init_subclass__(cls) -> None: ...

class staticmethod(Generic[_R]):  # Special, only valid as a decorator.
    __func__: Callable[..., _R]
    __isabstractmethod__: bool
    def __init__(self: staticmethod[_R], __f: Callable[..., _R]) -> None: ...
    def __new__(cls: Type[_T], *args: Any, **kwargs: Any) -> _T: ...
    def __get__(self, __obj: _T, __type: Type[_T] | None = ...) -> Callable[..., _R]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        __wrapped__: Callable[..., _R]
        def __call__(self, *args: Any, **kwargs: Any) -> _R: ...

class classmethod(Generic[_R]):  # Special, only valid as a decorator.
    __func__: Callable[..., _R]
    __isabstractmethod__: bool
    def __init__(self: classmethod[_R], __f: Callable[..., _R]) -> None: ...
    def __new__(cls: Type[_T], *args: Any, **kwargs: Any) -> _T: ...
    def __get__(self, __obj: _T, __type: Type[_T] | None = ...) -> Callable[..., _R]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        __wrapped__: Callable[..., _R]

class type(object):
    __base__: type
    __bases__: Tuple[type, ...]
    __basicsize__: int
    __dict__: dict[str, Any]
    __dictoffset__: int
    __flags__: int
    __itemsize__: int
    __module__: str
    __mro__: Tuple[type, ...]
    __name__: str
    __qualname__: str
    __text_signature__: str | None
    __weakrefoffset__: int
    @overload
    def __init__(self, __o: object) -> None: ...
    @overload
    def __init__(self, __name: str, __bases: Tuple[type, ...], __dict: dict[str, Any], **kwds: Any) -> None: ...
    @overload
    def __new__(cls, __o: object) -> type: ...
    @overload
    def __new__(cls: Type[_TT], __name: str, __bases: Tuple[type, ...], __namespace: dict[str, Any], **kwds: Any) -> _TT: ...
    def __call__(self, *args: Any, **kwds: Any) -> Any: ...
    def __subclasses__(self: _TT) -> list[_TT]: ...
    # Note: the documentation doesn't specify what the return type is, the standard
    # implementation seems to be returning a list.
    def mro(self) -> list[type]: ...
    def __instancecheck__(self, __instance: Any) -> bool: ...
    def __subclasscheck__(self, __subclass: type) -> bool: ...
    @classmethod
    def __prepare__(metacls, __name: str, __bases: Tuple[type, ...], **kwds: Any) -> Mapping[str, object]: ...
    if sys.version_info >= (3, 10):
        def __or__(self, __t: Any) -> types.UnionType: ...
        def __ror__(self, __t: Any) -> types.UnionType: ...

class super(object):
    @overload
    def __init__(self, __t: Any, __obj: Any) -> None: ...
    @overload
    def __init__(self, __t: Any) -> None: ...
    @overload
    def __init__(self) -> None: ...

_PositiveInteger = Literal[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
_NegativeInteger = Literal[-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20]

class int:
    @overload
    def __new__(cls: Type[_T], __x: str | bytes | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> _T: ...
    @overload
    def __new__(cls: Type[_T], __x: str | bytes | bytearray, base: SupportsIndex) -> _T: ...
    if sys.version_info >= (3, 8):
        def as_integer_ratio(self) -> tuple[int, Literal[1]]: ...
    @property
    def real(self) -> int: ...
    @property
    def imag(self) -> int: ...
    @property
    def numerator(self) -> int: ...
    @property
    def denominator(self) -> int: ...
    def conjugate(self) -> int: ...
    def bit_length(self) -> int: ...
    if sys.version_info >= (3, 10):
        def bit_count(self) -> int: ...
    def to_bytes(self, length: SupportsIndex, byteorder: Literal["little", "big"], *, signed: bool = ...) -> bytes: ...
    @classmethod
    def from_bytes(
        cls, bytes: Iterable[SupportsIndex] | SupportsBytes, byteorder: Literal["little", "big"], *, signed: bool = ...
    ) -> int: ...  # TODO buffer object argument
    def __add__(self, __x: int) -> int: ...
    def __sub__(self, __x: int) -> int: ...
    def __mul__(self, __x: int) -> int: ...
    def __floordiv__(self, __x: int) -> int: ...
    def __truediv__(self, __x: int) -> float: ...
    def __mod__(self, __x: int) -> int: ...
    def __divmod__(self, __x: int) -> tuple[int, int]: ...
    def __radd__(self, __x: int) -> int: ...
    def __rsub__(self, __x: int) -> int: ...
    def __rmul__(self, __x: int) -> int: ...
    def __rfloordiv__(self, __x: int) -> int: ...
    def __rtruediv__(self, __x: int) -> float: ...
    def __rmod__(self, __x: int) -> int: ...
    def __rdivmod__(self, __x: int) -> tuple[int, int]: ...
    @overload
    def __pow__(self, __x: int, __modulo: Literal[0]) -> NoReturn: ...
    @overload
    def __pow__(self, __x: int, __modulo: int) -> int: ...
    @overload
    def __pow__(self, __x: _PositiveInteger, __modulo: None = ...) -> int: ...
    @overload
    def __pow__(self, __x: _NegativeInteger, __modulo: None = ...) -> float: ...
    # positive x -> int; negative x -> float
    # return type must be Any as `int | float` causes too many false-positive errors
    @overload
    def __pow__(self, __x: int, __modulo: None = ...) -> Any: ...
    def __rpow__(self, __x: int, __mod: int | None = ...) -> Any: ...
    def __and__(self, __n: int) -> int: ...
    def __or__(self, __n: int) -> int: ...
    def __xor__(self, __n: int) -> int: ...
    def __lshift__(self, __n: int) -> int: ...
    def __rshift__(self, __n: int) -> int: ...
    def __rand__(self, __n: int) -> int: ...
    def __ror__(self, __n: int) -> int: ...
    def __rxor__(self, __n: int) -> int: ...
    def __rlshift__(self, __n: int) -> int: ...
    def __rrshift__(self, __n: int) -> int: ...
    def __neg__(self) -> int: ...
    def __pos__(self) -> int: ...
    def __invert__(self) -> int: ...
    def __trunc__(self) -> int: ...
    def __ceil__(self) -> int: ...
    def __floor__(self) -> int: ...
    def __round__(self, __ndigits: SupportsIndex = ...) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...
    def __eq__(self, __x: object) -> bool: ...
    def __ne__(self, __x: object) -> bool: ...
    def __lt__(self, __x: int) -> bool: ...
    def __le__(self, __x: int) -> bool: ...
    def __gt__(self, __x: int) -> bool: ...
    def __ge__(self, __x: int) -> bool: ...
    def __str__(self) -> str: ...
    def __float__(self) -> float: ...
    def __int__(self) -> int: ...
    def __abs__(self) -> int: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def __index__(self) -> int: ...

class float:
    def __new__(cls: Type[_T], x: SupportsFloat | SupportsIndex | str | bytes | bytearray = ...) -> _T: ...
    def as_integer_ratio(self) -> tuple[int, int]: ...
    def hex(self) -> str: ...
    def is_integer(self) -> bool: ...
    @classmethod
    def fromhex(cls, __s: str) -> float: ...
    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> float: ...
    def __add__(self, __x: float) -> float: ...
    def __sub__(self, __x: float) -> float: ...
    def __mul__(self, __x: float) -> float: ...
    def __floordiv__(self, __x: float) -> float: ...
    def __truediv__(self, __x: float) -> float: ...
    def __mod__(self, __x: float) -> float: ...
    def __divmod__(self, __x: float) -> tuple[float, float]: ...
    @overload
    def __pow__(self, __x: int, __mod: None = ...) -> float: ...
    # positive x -> float; negative x -> complex
    # return type must be Any as `float | complex` causes too many false-positive errors
    @overload
    def __pow__(self, __x: float, __mod: None = ...) -> Any: ...
    def __radd__(self, __x: float) -> float: ...
    def __rsub__(self, __x: float) -> float: ...
    def __rmul__(self, __x: float) -> float: ...
    def __rfloordiv__(self, __x: float) -> float: ...
    def __rtruediv__(self, __x: float) -> float: ...
    def __rmod__(self, __x: float) -> float: ...
    def __rdivmod__(self, __x: float) -> tuple[float, float]: ...
    def __rpow__(self, __x: float, __mod: None = ...) -> float: ...
    def __getnewargs__(self) -> tuple[float]: ...
    def __trunc__(self) -> int: ...
    if sys.version_info >= (3, 9):
        def __ceil__(self) -> int: ...
        def __floor__(self) -> int: ...
    @overload
    def __round__(self, __ndigits: None = ...) -> int: ...
    @overload
    def __round__(self, __ndigits: SupportsIndex) -> float: ...
    def __eq__(self, __x: object) -> bool: ...
    def __ne__(self, __x: object) -> bool: ...
    def __lt__(self, __x: float) -> bool: ...
    def __le__(self, __x: float) -> bool: ...
    def __gt__(self, __x: float) -> bool: ...
    def __ge__(self, __x: float) -> bool: ...
    def __neg__(self) -> float: ...
    def __pos__(self) -> float: ...
    def __str__(self) -> str: ...
    def __int__(self) -> int: ...
    def __float__(self) -> float: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...

class complex:
    @overload
    def __new__(cls: Type[_T], real: float = ..., imag: float = ...) -> _T: ...
    @overload
    def __new__(cls: Type[_T], real: str | SupportsComplex | SupportsIndex | complex) -> _T: ...
    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> complex: ...
    def __add__(self, __x: complex) -> complex: ...
    def __sub__(self, __x: complex) -> complex: ...
    def __mul__(self, __x: complex) -> complex: ...
    def __pow__(self, __x: complex, mod: None = ...) -> complex: ...
    def __truediv__(self, __x: complex) -> complex: ...
    def __radd__(self, __x: complex) -> complex: ...
    def __rsub__(self, __x: complex) -> complex: ...
    def __rmul__(self, __x: complex) -> complex: ...
    def __rpow__(self, __x: complex, __mod: None = ...) -> complex: ...
    def __rtruediv__(self, __x: complex) -> complex: ...
    def __eq__(self, __x: object) -> bool: ...
    def __ne__(self, __x: object) -> bool: ...
    def __neg__(self) -> complex: ...
    def __pos__(self) -> complex: ...
    def __str__(self) -> str: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...

class _FormatMapMapping(Protocol):
    def __getitem__(self, __key: str) -> Any: ...

class str(Sequence[str]):
    @overload
    def __new__(cls: Type[_T], object: object = ...) -> _T: ...
    @overload
    def __new__(cls: Type[_T], o: bytes, encoding: str = ..., errors: str = ...) -> _T: ...
    def capitalize(self) -> str: ...
    def casefold(self) -> str: ...
    def center(self, __width: SupportsIndex, __fillchar: str = ...) -> str: ...
    def count(self, x: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def encode(self, encoding: str = ..., errors: str = ...) -> bytes: ...
    def endswith(
        self, __suffix: str | Tuple[str, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = ...) -> str: ...
    else:
        def expandtabs(self, tabsize: int = ...) -> str: ...
    def find(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def format(self, *args: object, **kwargs: object) -> str: ...
    def format_map(self, map: _FormatMapMapping) -> str: ...
    def index(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    if sys.version_info >= (3, 7):
        def isascii(self) -> bool: ...
    def isdecimal(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def isidentifier(self) -> bool: ...
    def islower(self) -> bool: ...
    def isnumeric(self) -> bool: ...
    def isprintable(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable: Iterable[str]) -> str: ...
    def ljust(self, __width: SupportsIndex, __fillchar: str = ...) -> str: ...
    def lower(self) -> str: ...
    def lstrip(self, __chars: str | None = ...) -> str: ...
    def partition(self, __sep: str) -> tuple[str, str, str]: ...
    def replace(self, __old: str, __new: str, __count: SupportsIndex = ...) -> str: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: str) -> str: ...
        def removesuffix(self, __suffix: str) -> str: ...
    def rfind(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def rindex(self, __sub: str, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: str = ...) -> str: ...
    def rpartition(self, __sep: str) -> tuple[str, str, str]: ...
    def rsplit(self, sep: str | None = ..., maxsplit: SupportsIndex = ...) -> list[str]: ...
    def rstrip(self, __chars: str | None = ...) -> str: ...
    def split(self, sep: str | None = ..., maxsplit: SupportsIndex = ...) -> list[str]: ...
    def splitlines(self, keepends: bool = ...) -> list[str]: ...
    def startswith(
        self, __prefix: str | Tuple[str, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    def strip(self, __chars: str | None = ...) -> str: ...
    def swapcase(self) -> str: ...
    def title(self) -> str: ...
    def translate(self, __table: Mapping[int, int | str | None] | Sequence[int | str | None]) -> str: ...
    def upper(self) -> str: ...
    def zfill(self, __width: SupportsIndex) -> str: ...
    @staticmethod
    @overload
    def maketrans(__x: dict[int, _T] | dict[str, _T] | dict[str | int, _T]) -> dict[int, _T]: ...
    @staticmethod
    @overload
    def maketrans(__x: str, __y: str, __z: str | None = ...) -> dict[int, int | None]: ...
    def __add__(self, __s: str) -> str: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, __o: str) -> bool: ...  # type: ignore[override]
    def __eq__(self, __x: object) -> bool: ...
    def __ge__(self, __x: str) -> bool: ...
    def __getitem__(self, __i: int | slice) -> str: ...
    def __gt__(self, __x: str) -> bool: ...
    def __hash__(self) -> int: ...
    def __iter__(self) -> Iterator[str]: ...
    def __le__(self, __x: str) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, __x: str) -> bool: ...
    def __mod__(self, __x: Any) -> str: ...
    def __mul__(self, __n: SupportsIndex) -> str: ...
    def __ne__(self, __x: object) -> bool: ...
    def __repr__(self) -> str: ...
    def __rmul__(self, n: SupportsIndex) -> str: ...
    def __str__(self) -> str: ...
    def __getnewargs__(self) -> tuple[str]: ...

class bytes(ByteString):
    @overload
    def __new__(cls: Type[_T], __ints: Iterable[SupportsIndex]) -> _T: ...
    @overload
    def __new__(cls: Type[_T], __string: str, encoding: str, errors: str = ...) -> _T: ...
    @overload
    def __new__(cls: Type[_T], __length: SupportsIndex) -> _T: ...
    @overload
    def __new__(cls: Type[_T]) -> _T: ...
    @overload
    def __new__(cls: Type[_T], __o: SupportsBytes) -> _T: ...
    def capitalize(self) -> bytes: ...
    def center(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytes: ...
    def count(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def decode(self, encoding: str = ..., errors: str = ...) -> str: ...
    def endswith(
        self, __suffix: bytes | Tuple[bytes, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = ...) -> bytes: ...
    else:
        def expandtabs(self, tabsize: int = ...) -> bytes: ...
    def find(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...
    def index(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    if sys.version_info >= (3, 7):
        def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable_of_bytes: Iterable[ByteString | memoryview]) -> bytes: ...
    def ljust(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytes: ...
    def lower(self) -> bytes: ...
    def lstrip(self, __bytes: bytes | None = ...) -> bytes: ...
    def partition(self, __sep: bytes) -> tuple[bytes, bytes, bytes]: ...
    def replace(self, __old: bytes, __new: bytes, __count: SupportsIndex = ...) -> bytes: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: bytes) -> bytes: ...
        def removesuffix(self, __suffix: bytes) -> bytes: ...
    def rfind(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rindex(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytes: ...
    def rpartition(self, __sep: bytes) -> tuple[bytes, bytes, bytes]: ...
    def rsplit(self, sep: bytes | None = ..., maxsplit: SupportsIndex = ...) -> list[bytes]: ...
    def rstrip(self, __bytes: bytes | None = ...) -> bytes: ...
    def split(self, sep: bytes | None = ..., maxsplit: SupportsIndex = ...) -> list[bytes]: ...
    def splitlines(self, keepends: bool = ...) -> list[bytes]: ...
    def startswith(
        self, __prefix: bytes | Tuple[bytes, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    def strip(self, __bytes: bytes | None = ...) -> bytes: ...
    def swapcase(self) -> bytes: ...
    def title(self) -> bytes: ...
    def translate(self, __table: bytes | None, delete: bytes = ...) -> bytes: ...
    def upper(self) -> bytes: ...
    def zfill(self, __width: SupportsIndex) -> bytes: ...
    @classmethod
    def fromhex(cls: Type[_T], __s: str) -> _T: ...
    @staticmethod
    def maketrans(__frm: bytes, __to: bytes) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __hash__(self) -> int: ...
    @overload
    def __getitem__(self, __i: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __s: slice) -> bytes: ...
    def __add__(self, __s: bytes) -> bytes: ...
    def __mul__(self, __n: SupportsIndex) -> bytes: ...
    def __rmul__(self, __n: SupportsIndex) -> bytes: ...
    def __mod__(self, __value: Any) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, __o: SupportsIndex | bytes) -> bool: ...  # type: ignore[override]
    def __eq__(self, __x: object) -> bool: ...
    def __ne__(self, __x: object) -> bool: ...
    def __lt__(self, __x: bytes) -> bool: ...
    def __le__(self, __x: bytes) -> bool: ...
    def __gt__(self, __x: bytes) -> bool: ...
    def __ge__(self, __x: bytes) -> bool: ...
    def __getnewargs__(self) -> tuple[bytes]: ...

class bytearray(MutableSequence[int], ByteString):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, __ints: Iterable[SupportsIndex]) -> None: ...
    @overload
    def __init__(self, __string: str, encoding: str, errors: str = ...) -> None: ...
    @overload
    def __init__(self, __length: SupportsIndex) -> None: ...
    def append(self, __item: SupportsIndex) -> None: ...
    def capitalize(self) -> bytearray: ...
    def center(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytearray: ...
    def count(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def copy(self) -> bytearray: ...
    def decode(self, encoding: str = ..., errors: str = ...) -> str: ...
    def endswith(
        self, __suffix: bytes | Tuple[bytes, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    if sys.version_info >= (3, 8):
        def expandtabs(self, tabsize: SupportsIndex = ...) -> bytearray: ...
    else:
        def expandtabs(self, tabsize: int = ...) -> bytearray: ...
    def extend(self, __iterable_of_ints: Iterable[SupportsIndex]) -> None: ...
    def find(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...
    def index(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def insert(self, __index: SupportsIndex, __item: SupportsIndex) -> None: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    if sys.version_info >= (3, 7):
        def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, __iterable_of_bytes: Iterable[ByteString | memoryview]) -> bytearray: ...
    def ljust(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytearray: ...
    def lower(self) -> bytearray: ...
    def lstrip(self, __bytes: bytes | None = ...) -> bytearray: ...
    def partition(self, __sep: bytes) -> tuple[bytearray, bytearray, bytearray]: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, __prefix: bytes) -> bytearray: ...
        def removesuffix(self, __suffix: bytes) -> bytearray: ...
    def replace(self, __old: bytes, __new: bytes, __count: SupportsIndex = ...) -> bytearray: ...
    def rfind(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rindex(
        self, __sub: bytes | SupportsIndex, __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> int: ...
    def rjust(self, __width: SupportsIndex, __fillchar: bytes = ...) -> bytearray: ...
    def rpartition(self, __sep: bytes) -> tuple[bytearray, bytearray, bytearray]: ...
    def rsplit(self, sep: bytes | None = ..., maxsplit: SupportsIndex = ...) -> list[bytearray]: ...
    def rstrip(self, __bytes: bytes | None = ...) -> bytearray: ...
    def split(self, sep: bytes | None = ..., maxsplit: SupportsIndex = ...) -> list[bytearray]: ...
    def splitlines(self, keepends: bool = ...) -> list[bytearray]: ...
    def startswith(
        self, __prefix: bytes | Tuple[bytes, ...], __start: SupportsIndex | None = ..., __end: SupportsIndex | None = ...
    ) -> bool: ...
    def strip(self, __bytes: bytes | None = ...) -> bytearray: ...
    def swapcase(self) -> bytearray: ...
    def title(self) -> bytearray: ...
    def translate(self, __table: bytes | None, delete: bytes = ...) -> bytearray: ...
    def upper(self) -> bytearray: ...
    def zfill(self, __width: SupportsIndex) -> bytearray: ...
    @classmethod
    def fromhex(cls, __string: str) -> bytearray: ...
    @staticmethod
    def maketrans(__frm: bytes, __to: bytes) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    __hash__: None  # type: ignore[assignment]
    @overload
    def __getitem__(self, __i: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __s: slice) -> bytearray: ...
    @overload
    def __setitem__(self, __i: SupportsIndex, x: SupportsIndex) -> None: ...
    @overload
    def __setitem__(self, __s: slice, __x: Iterable[SupportsIndex] | bytes) -> None: ...
    def __delitem__(self, __i: SupportsIndex | slice) -> None: ...
    def __add__(self, __s: bytes) -> bytearray: ...
    def __iadd__(self, __s: Iterable[int]) -> bytearray: ...
    def __mul__(self, __n: SupportsIndex) -> bytearray: ...
    def __rmul__(self, __n: SupportsIndex) -> bytearray: ...
    def __imul__(self, __n: SupportsIndex) -> bytearray: ...
    def __mod__(self, __value: Any) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, __o: SupportsIndex | bytes) -> bool: ...  # type: ignore[override]
    def __eq__(self, __x: object) -> bool: ...
    def __ne__(self, __x: object) -> bool: ...
    def __lt__(self, __x: bytes) -> bool: ...
    def __le__(self, __x: bytes) -> bool: ...
    def __gt__(self, __x: bytes) -> bool: ...
    def __ge__(self, __x: bytes) -> bool: ...

@final
class memoryview(Sized, Sequence[int]):
    format: str
    itemsize: int
    shape: Tuple[int, ...] | None
    strides: Tuple[int, ...] | None
    suboffsets: Tuple[int, ...] | None
    readonly: bool
    ndim: int

    obj: bytes | bytearray
    c_contiguous: bool
    f_contiguous: bool
    contiguous: bool
    nbytes: int
    def __init__(self, obj: ReadableBuffer) -> None: ...
    def __enter__(self: Self) -> Self: ...
    def __exit__(
        self, __exc_type: Type[BaseException] | None, __exc_val: BaseException | None, __exc_tb: TracebackType | None
    ) -> None: ...
    def cast(self, format: str, shape: list[int] | Tuple[int, ...] = ...) -> memoryview: ...
    @overload
    def __getitem__(self, __i: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __s: slice) -> memoryview: ...
    def __contains__(self, __x: object) -> bool: ...
    def __iter__(self) -> Iterator[int]: ...
    def __len__(self) -> int: ...
    @overload
    def __setitem__(self, __s: slice, __o: bytes) -> None: ...
    @overload
    def __setitem__(self, __i: SupportsIndex, __o: SupportsIndex) -> None: ...
    if sys.version_info >= (3, 8):
        def tobytes(self, order: Literal["C", "F", "A"] | None = ...) -> bytes: ...
    else:
        def tobytes(self) -> bytes: ...
    def tolist(self) -> list[int]: ...
    if sys.version_info >= (3, 8):
        def toreadonly(self) -> memoryview: ...
    def release(self) -> None: ...
    if sys.version_info >= (3, 8):
        def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    else:
        def hex(self) -> str: ...

@final
class bool(int):
    def __new__(cls: Type[_T], __o: object = ...) -> _T: ...
    @overload
    def __and__(self, __x: bool) -> bool: ...
    @overload
    def __and__(self, __x: int) -> int: ...
    @overload
    def __or__(self, __x: bool) -> bool: ...
    @overload
    def __or__(self, __x: int) -> int: ...
    @overload
    def __xor__(self, __x: bool) -> bool: ...
    @overload
    def __xor__(self, __x: int) -> int: ...
    @overload
    def __rand__(self, __x: bool) -> bool: ...
    @overload
    def __rand__(self, __x: int) -> int: ...
    @overload
    def __ror__(self, __x: bool) -> bool: ...
    @overload
    def __ror__(self, __x: int) -> int: ...
    @overload
    def __rxor__(self, __x: bool) -> bool: ...
    @overload
    def __rxor__(self, __x: int) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...

@final
class slice(object):
    start: Any
    step: Any
    stop: Any
    @overload
    def __init__(self, __stop: Any) -> None: ...
    @overload
    def __init__(self, __start: Any, __stop: Any, __step: Any = ...) -> None: ...
    __hash__: None  # type: ignore[assignment]
    def indices(self, __len: SupportsIndex) -> tuple[int, int, int]: ...

class tuple(Sequence[_T_co], Generic[_T_co]):
    def __new__(cls: Type[_T], __iterable: Iterable[_T_co] = ...) -> _T: ...
    def __len__(self) -> int: ...
    def __contains__(self, __x: object) -> bool: ...
    @overload
    def __getitem__(self, __x: int) -> _T_co: ...
    @overload
    def __getitem__(self, __x: slice) -> Tuple[_T_co, ...]: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __lt__(self, __x: Tuple[_T_co, ...]) -> bool: ...
    def __le__(self, __x: Tuple[_T_co, ...]) -> bool: ...
    def __gt__(self, __x: Tuple[_T_co, ...]) -> bool: ...
    def __ge__(self, __x: Tuple[_T_co, ...]) -> bool: ...
    @overload
    def __add__(self, __x: Tuple[_T_co, ...]) -> Tuple[_T_co, ...]: ...
    @overload
    def __add__(self, __x: Tuple[_T, ...]) -> Tuple[_T_co | _T, ...]: ...
    def __mul__(self, __n: SupportsIndex) -> Tuple[_T_co, ...]: ...
    def __rmul__(self, __n: SupportsIndex) -> Tuple[_T_co, ...]: ...
    def count(self, __value: Any) -> int: ...
    def index(self, __value: Any, __start: SupportsIndex = ..., __stop: SupportsIndex = ...) -> int: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class function:
    # TODO not defined in builtins!
    __name__: str
    __module__: str
    __code__: CodeType
    __qualname__: str
    __annotations__: dict[str, Any]

class list(MutableSequence[_T], Generic[_T]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, __iterable: Iterable[_T]) -> None: ...
    def clear(self) -> None: ...
    def copy(self) -> list[_T]: ...
    def append(self, __object: _T) -> None: ...
    def extend(self, __iterable: Iterable[_T]) -> None: ...
    def pop(self, __index: SupportsIndex = ...) -> _T: ...
    def index(self, __value: _T, __start: SupportsIndex = ..., __stop: SupportsIndex = ...) -> int: ...
    def count(self, __value: _T) -> int: ...
    def insert(self, __index: SupportsIndex, __object: _T) -> None: ...
    def remove(self, __value: _T) -> None: ...
    def reverse(self) -> None: ...
    @overload
    def sort(self: list[SupportsRichComparisonT], *, key: None = ..., reverse: bool = ...) -> None: ...
    @overload
    def sort(self, *, key: Callable[[_T], SupportsRichComparison], reverse: bool = ...) -> None: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __str__(self) -> str: ...
    __hash__: None  # type: ignore[assignment]
    @overload
    def __getitem__(self, __i: SupportsIndex) -> _T: ...
    @overload
    def __getitem__(self, __s: slice) -> list[_T]: ...
    @overload
    def __setitem__(self, __i: SupportsIndex, __o: _T) -> None: ...
    @overload
    def __setitem__(self, __s: slice, __o: Iterable[_T]) -> None: ...
    def __delitem__(self, __i: SupportsIndex | slice) -> None: ...
    def __add__(self, __x: list[_T]) -> list[_T]: ...
    def __iadd__(self: _S, __x: Iterable[_T]) -> _S: ...
    def __mul__(self, __n: SupportsIndex) -> list[_T]: ...
    def __rmul__(self, __n: SupportsIndex) -> list[_T]: ...
    def __imul__(self: _S, __n: SupportsIndex) -> _S: ...
    def __contains__(self, __o: object) -> bool: ...
    def __reversed__(self) -> Iterator[_T]: ...
    def __gt__(self, __x: list[_T]) -> bool: ...
    def __ge__(self, __x: list[_T]) -> bool: ...
    def __lt__(self, __x: list[_T]) -> bool: ...
    def __le__(self, __x: list[_T]) -> bool: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class dict(MutableMapping[_KT, _VT], Generic[_KT, _VT]):
    @overload
    def __init__(self: dict[_KT, _VT]) -> None: ...
    @overload
    def __init__(self: dict[str, _VT], **kwargs: _VT) -> None: ...
    @overload
    def __init__(self, __map: SupportsKeysAndGetItem[_KT, _VT], **kwargs: _VT) -> None: ...
    @overload
    def __init__(self, __iterable: Iterable[tuple[_KT, _VT]], **kwargs: _VT) -> None: ...
    # Next overload is for dict(string.split(sep) for string in iterable)
    # Cannot be Iterable[Sequence[_T]] or otherwise dict(["foo", "bar", "baz"]) is not an error
    @overload
    def __init__(self: dict[str, str], __iterable: Iterable[list[str]]) -> None: ...
    def __new__(cls: Type[_T1], *args: Any, **kwargs: Any) -> _T1: ...
    def clear(self) -> None: ...
    def copy(self) -> dict[_KT, _VT]: ...
    def popitem(self) -> tuple[_KT, _VT]: ...
    def setdefault(self, __key: _KT, __default: _VT = ...) -> _VT: ...
    @overload
    def update(self, __m: Mapping[_KT, _VT], **kwargs: _VT) -> None: ...
    @overload
    def update(self, __m: Iterable[tuple[_KT, _VT]], **kwargs: _VT) -> None: ...
    @overload
    def update(self, **kwargs: _VT) -> None: ...
    def keys(self) -> dict_keys[_KT, _VT]: ...
    def values(self) -> dict_values[_KT, _VT]: ...
    def items(self) -> dict_items[_KT, _VT]: ...
    @classmethod
    @overload
    def fromkeys(cls, __iterable: Iterable[_T], __value: None = ...) -> dict[_T, Any | None]: ...
    @classmethod
    @overload
    def fromkeys(cls, __iterable: Iterable[_T], __value: _S) -> dict[_T, _S]: ...
    def __len__(self) -> int: ...
    def __getitem__(self, __k: _KT) -> _VT: ...
    def __setitem__(self, __k: _KT, v: _VT) -> None: ...
    def __delitem__(self, __v: _KT) -> None: ...
    def __iter__(self) -> Iterator[_KT]: ...
    if sys.version_info >= (3, 8):
        def __reversed__(self) -> Iterator[_KT]: ...
    def __str__(self) -> str: ...
    __hash__: None  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...
        def __or__(self, __value: Mapping[_T1, _T2]) -> dict[_KT | _T1, _VT | _T2]: ...
        def __ror__(self, __value: Mapping[_T1, _T2]) -> dict[_KT | _T1, _VT | _T2]: ...
        def __ior__(self, __value: Mapping[_KT, _VT]) -> dict[_KT, _VT]: ...  # type: ignore[misc]

class set(MutableSet[_T], Generic[_T]):
    def __init__(self, __iterable: Iterable[_T] = ...) -> None: ...
    def add(self, __element: _T) -> None: ...
    def clear(self) -> None: ...
    def copy(self) -> set[_T]: ...
    def difference(self, *s: Iterable[Any]) -> set[_T]: ...
    def difference_update(self, *s: Iterable[Any]) -> None: ...
    def discard(self, __element: _T) -> None: ...
    def intersection(self, *s: Iterable[Any]) -> set[_T]: ...
    def intersection_update(self, *s: Iterable[Any]) -> None: ...
    def isdisjoint(self, __s: Iterable[Any]) -> bool: ...
    def issubset(self, __s: Iterable[Any]) -> bool: ...
    def issuperset(self, __s: Iterable[Any]) -> bool: ...
    def pop(self) -> _T: ...
    def remove(self, __element: _T) -> None: ...
    def symmetric_difference(self, __s: Iterable[_T]) -> set[_T]: ...
    def symmetric_difference_update(self, __s: Iterable[_T]) -> None: ...
    def union(self, *s: Iterable[_S]) -> set[_T | _S]: ...
    def update(self, *s: Iterable[_T]) -> None: ...
    def __len__(self) -> int: ...
    def __contains__(self, __o: object) -> bool: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __str__(self) -> str: ...
    def __and__(self, __s: AbstractSet[object]) -> set[_T]: ...
    def __iand__(self, __s: AbstractSet[object]) -> set[_T]: ...
    def __or__(self, __s: AbstractSet[_S]) -> set[_T | _S]: ...
    def __ior__(self, __s: AbstractSet[_S]) -> set[_T | _S]: ...
    def __sub__(self, __s: AbstractSet[_T | None]) -> set[_T]: ...
    def __isub__(self, __s: AbstractSet[_T | None]) -> set[_T]: ...
    def __xor__(self, __s: AbstractSet[_S]) -> set[_T | _S]: ...
    def __ixor__(self, __s: AbstractSet[_S]) -> set[_T | _S]: ...
    def __le__(self, __s: AbstractSet[object]) -> bool: ...
    def __lt__(self, __s: AbstractSet[object]) -> bool: ...
    def __ge__(self, __s: AbstractSet[object]) -> bool: ...
    def __gt__(self, __s: AbstractSet[object]) -> bool: ...
    __hash__: None  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class frozenset(AbstractSet[_T_co], Generic[_T_co]):
    def __init__(self, __iterable: Iterable[_T_co] = ...) -> None: ...
    def copy(self) -> frozenset[_T_co]: ...
    def difference(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def intersection(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def isdisjoint(self, __s: Iterable[_T_co]) -> bool: ...
    def issubset(self, __s: Iterable[object]) -> bool: ...
    def issuperset(self, __s: Iterable[object]) -> bool: ...
    def symmetric_difference(self, __s: Iterable[_T_co]) -> frozenset[_T_co]: ...
    def union(self, *s: Iterable[_S]) -> frozenset[_T_co | _S]: ...
    def __len__(self) -> int: ...
    def __contains__(self, __o: object) -> bool: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __str__(self) -> str: ...
    def __and__(self, __s: AbstractSet[_T_co]) -> frozenset[_T_co]: ...
    def __or__(self, __s: AbstractSet[_S]) -> frozenset[_T_co | _S]: ...
    def __sub__(self, __s: AbstractSet[_T_co]) -> frozenset[_T_co]: ...
    def __xor__(self, __s: AbstractSet[_S]) -> frozenset[_T_co | _S]: ...
    def __le__(self, __s: AbstractSet[object]) -> bool: ...
    def __lt__(self, __s: AbstractSet[object]) -> bool: ...
    def __ge__(self, __s: AbstractSet[object]) -> bool: ...
    def __gt__(self, __s: AbstractSet[object]) -> bool: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

class enumerate(Iterator[Tuple[int, _T]], Generic[_T]):
    def __init__(self, iterable: Iterable[_T], start: int = ...) -> None: ...
    def __iter__(self) -> Iterator[tuple[int, _T]]: ...
    def __next__(self) -> tuple[int, _T]: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, __item: Any) -> GenericAlias: ...

@final
class range(Sequence[int]):
    start: int
    stop: int
    step: int
    @overload
    def __init__(self, __stop: SupportsIndex) -> None: ...
    @overload
    def __init__(self, __start: SupportsIndex, __stop: SupportsIndex, __step: SupportsIndex = ...) -> None: ...
    def count(self, __value: int) -> int: ...
    def index(self, __value: int) -> int: ...  # type: ignore[override]
    def __len__(self) -> int: ...
    def __contains__(self, __o: object) -> bool: ...
    def __iter__(self) -> Iterator[int]: ...
    @overload
    def __getitem__(self, __i: SupportsIndex) -> int: ...
    @overload
    def __getitem__(self, __s: slice) -> range: ...
    def __repr__(self) -> str: ...
    def __reversed__(self) -> Iterator[int]: ...

class property(object):
    fget: Callable[[Any], Any] | None
    fset: Callable[[Any, Any], None] | None
    fdel: Callable[[Any], None] | None
    def __init__(
        self,
        fget: Callable[[Any], Any] | None = ...,
        fset: Callable[[Any, Any], None] | None = ...,
        fdel: Callable[[Any], None] | None = ...,
        doc: str | None = ...,
    ) -> None: ...
    def getter(self, __fget: Callable[[Any], Any]) -> property: ...
    def setter(self, __fset: Callable[[Any, Any], None]) -> property: ...
    def deleter(self, __fdel: Callable[[Any], None]) -> property: ...
    def __get__(self, __obj: Any, __type: type | None = ...) -> Any: ...
    def __set__(self, __obj: Any, __value: Any) -> None: ...
    def __delete__(self, __obj: Any) -> None: ...

@final
class _NotImplementedType(Any):  # type: ignore[misc]
    # A little weird, but typing the __call__ as NotImplemented makes the error message
    # for NotImplemented() much better
    __call__: NotImplemented  # type: ignore[valid-type]

NotImplemented: _NotImplementedType

def abs(__x: SupportsAbs[_T]) -> _T: ...
def all(__iterable: Iterable[object]) -> bool: ...
def any(__iterable: Iterable[object]) -> bool: ...
def ascii(__obj: object) -> str: ...
def bin(__number: int | SupportsIndex) -> str: ...

if sys.version_info >= (3, 7):
    def breakpoint(*args: Any, **kws: Any) -> None: ...

def callable(__obj: object) -> TypeGuard[Callable[..., object]]: ...
def chr(__i: int) -> str: ...

# We define this here instead of using os.PathLike to avoid import cycle issues.
# See https://github.com/python/typeshed/pull/991#issuecomment-288160993
_AnyStr_co = TypeVar("_AnyStr_co", str, bytes, covariant=True)

class _PathLike(Protocol[_AnyStr_co]):
    def __fspath__(self) -> _AnyStr_co: ...

if sys.version_info >= (3, 10):
    def aiter(__iterable: AsyncIterable[_T]) -> AsyncIterator[_T]: ...
    @overload
    async def anext(__i: SupportsAnext[_T]) -> _T: ...
    @overload
    async def anext(__i: SupportsAnext[_T], default: _VT) -> _T | _VT: ...

# TODO: `compile` has a more precise return type in reality; work on a way of expressing that?
if sys.version_info >= (3, 8):
    def compile(
        source: str | bytes | AST,
        filename: str | bytes | _PathLike[Any],
        mode: str,
        flags: int = ...,
        dont_inherit: int = ...,
        optimize: int = ...,
        *,
        _feature_version: int = ...,
    ) -> Any: ...

else:
    def compile(
        source: str | bytes | AST,
        filename: str | bytes | _PathLike[Any],
        mode: str,
        flags: int = ...,
        dont_inherit: int = ...,
        optimize: int = ...,
    ) -> Any: ...

def copyright() -> None: ...
def credits() -> None: ...
def delattr(__obj: object, __name: str) -> None: ...
def dir(__o: object = ...) -> list[str]: ...
@overload
def divmod(__x: SupportsDivMod[_T_contra, _T_co], __y: _T_contra) -> _T_co: ...
@overload
def divmod(__x: _T_contra, __y: SupportsRDivMod[_T_contra, _T_co]) -> _T_co: ...

# The `globals` argument to `eval` has to be `dict[str, Any]` rather than `dict[str, object]` due to invariance.
# (The `globals` argument has to be a "real dict", rather than any old mapping, unlike the `locals` argument.)
def eval(
    __source: str | bytes | CodeType, __globals: dict[str, Any] | None = ..., __locals: Mapping[str, object] | None = ...
) -> Any: ...

# Comment above regarding `eval` applies to `exec` as well
def exec(
    __source: str | bytes | CodeType, __globals: dict[str, Any] | None = ..., __locals: Mapping[str, object] | None = ...
) -> None: ...
def exit(code: object = ...) -> NoReturn: ...

class filter(Iterator[_T], Generic[_T]):
    @overload
    def __init__(self, __function: None, __iterable: Iterable[_T | None]) -> None: ...
    @overload
    def __init__(self, __function: Callable[[_T], Any], __iterable: Iterable[_T]) -> None: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __next__(self) -> _T: ...

def format(__value: object, __format_spec: str = ...) -> str: ...  # TODO unicode
@overload
def getattr(__o: object, __name: str) -> Any: ...

# While technically covered by the last overload, spelling out the types for None, bool
# and basic containers help mypy out in some tricky situations involving type context
# (aka bidirectional inference)
@overload
def getattr(__o: object, __name: str, __default: None) -> Any | None: ...
@overload
def getattr(__o: object, __name: str, __default: bool) -> Any | bool: ...
@overload
def getattr(__o: object, name: str, __default: list[Any]) -> Any | list[Any]: ...
@overload
def getattr(__o: object, name: str, __default: dict[Any, Any]) -> Any | dict[Any, Any]: ...
@overload
def getattr(__o: object, __name: str, __default: _T) -> Any | _T: ...
def globals() -> dict[str, Any]: ...
def hasattr(__obj: object, __name: str) -> bool: ...
def hash(__obj: object) -> int: ...
def help(*args: Any, **kwds: Any) -> None: ...
def hex(__number: int | SupportsIndex) -> str: ...
def id(__obj: object) -> int: ...
def input(__prompt: object = ...) -> str: ...
@overload
def iter(__iterable: Iterable[_T]) -> Iterator[_T]: ...
@overload
def iter(__function: Callable[[], _T | None], __sentinel: None) -> Iterator[_T]: ...
@overload
def iter(__function: Callable[[], _T], __sentinel: object) -> Iterator[_T]: ...

# We need recursive types to express the type of the second argument to `isinstance` properly, hence the use of `Any`
if sys.version_info >= (3, 10):
    def isinstance(
        __obj: object, __class_or_tuple: type | types.UnionType | Tuple[type | types.UnionType | Tuple[Any, ...], ...]
    ) -> bool: ...
    def issubclass(
        __cls: type, __class_or_tuple: type | types.UnionType | Tuple[type | types.UnionType | Tuple[Any, ...], ...]
    ) -> bool: ...

else:
    def isinstance(__obj: object, __class_or_tuple: type | Tuple[type | Tuple[Any, ...], ...]) -> bool: ...
    def issubclass(__cls: type, __class_or_tuple: type | Tuple[type | Tuple[Any, ...], ...]) -> bool: ...

def len(__obj: Sized) -> int: ...
def license() -> None: ...
def locals() -> dict[str, Any]: ...

class map(Iterator[_S], Generic[_S]):
    @overload
    def __init__(self, __func: Callable[[_T1], _S], __iter1: Iterable[_T1]) -> None: ...
    @overload
    def __init__(self, __func: Callable[[_T1, _T2], _S], __iter1: Iterable[_T1], __iter2: Iterable[_T2]) -> None: ...
    @overload
    def __init__(
        self, __func: Callable[[_T1, _T2, _T3], _S], __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3]
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[[_T1, _T2, _T3, _T4], _S],
        __iter1: Iterable[_T1],
        __iter2: Iterable[_T2],
        __iter3: Iterable[_T3],
        __iter4: Iterable[_T4],
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[[_T1, _T2, _T3, _T4, _T5], _S],
        __iter1: Iterable[_T1],
        __iter2: Iterable[_T2],
        __iter3: Iterable[_T3],
        __iter4: Iterable[_T4],
        __iter5: Iterable[_T5],
    ) -> None: ...
    @overload
    def __init__(
        self,
        __func: Callable[..., _S],
        __iter1: Iterable[Any],
        __iter2: Iterable[Any],
        __iter3: Iterable[Any],
        __iter4: Iterable[Any],
        __iter5: Iterable[Any],
        __iter6: Iterable[Any],
        *iterables: Iterable[Any],
    ) -> None: ...
    def __iter__(self) -> Iterator[_S]: ...
    def __next__(self) -> _S: ...

@overload
def max(
    __arg1: SupportsRichComparisonT, __arg2: SupportsRichComparisonT, *_args: SupportsRichComparisonT, key: None = ...
) -> SupportsRichComparisonT: ...
@overload
def max(__arg1: _T, __arg2: _T, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(__iterable: Iterable[SupportsRichComparisonT], *, key: None = ...) -> SupportsRichComparisonT: ...
@overload
def max(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(__iterable: Iterable[SupportsRichComparisonT], *, key: None = ..., default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def max(__iterable: Iterable[_T1], *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def min(
    __arg1: SupportsRichComparisonT, __arg2: SupportsRichComparisonT, *_args: SupportsRichComparisonT, key: None = ...
) -> SupportsRichComparisonT: ...
@overload
def min(__arg1: _T, __arg2: _T, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(__iterable: Iterable[SupportsRichComparisonT], *, key: None = ...) -> SupportsRichComparisonT: ...
@overload
def min(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(__iterable: Iterable[SupportsRichComparisonT], *, key: None = ..., default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def min(__iterable: Iterable[_T1], *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def next(__i: SupportsNext[_T]) -> _T: ...
@overload
def next(__i: SupportsNext[_T], __default: _VT) -> _T | _VT: ...
def oct(__number: int | SupportsIndex) -> str: ...

_OpenFile = Union[StrOrBytesPath, int]
_Opener = Callable[[str, int], int]

# Text mode: always returns a TextIOWrapper
@overload
def open(
    file: _OpenFile,
    mode: OpenTextMode = ...,
    buffering: int = ...,
    encoding: str | None = ...,
    errors: str | None = ...,
    newline: str | None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> TextIOWrapper: ...

# Unbuffered binary mode: returns a FileIO
@overload
def open(
    file: _OpenFile,
    mode: OpenBinaryMode,
    buffering: Literal[0],
    encoding: None = ...,
    errors: None = ...,
    newline: None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> FileIO: ...

# Buffering is on: return BufferedRandom, BufferedReader, or BufferedWriter
@overload
def open(
    file: _OpenFile,
    mode: OpenBinaryModeUpdating,
    buffering: Literal[-1, 1] = ...,
    encoding: None = ...,
    errors: None = ...,
    newline: None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> BufferedRandom: ...
@overload
def open(
    file: _OpenFile,
    mode: OpenBinaryModeWriting,
    buffering: Literal[-1, 1] = ...,
    encoding: None = ...,
    errors: None = ...,
    newline: None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> BufferedWriter: ...
@overload
def open(
    file: _OpenFile,
    mode: OpenBinaryModeReading,
    buffering: Literal[-1, 1] = ...,
    encoding: None = ...,
    errors: None = ...,
    newline: None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> BufferedReader: ...

# Buffering cannot be determined: fall back to BinaryIO
@overload
def open(
    file: _OpenFile,
    mode: OpenBinaryMode,
    buffering: int,
    encoding: None = ...,
    errors: None = ...,
    newline: None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> BinaryIO: ...

# Fallback if mode is not specified
@overload
def open(
    file: _OpenFile,
    mode: str,
    buffering: int = ...,
    encoding: str | None = ...,
    errors: str | None = ...,
    newline: str | None = ...,
    closefd: bool = ...,
    opener: _Opener | None = ...,
) -> IO[Any]: ...
def ord(__c: str | bytes) -> int: ...

class _SupportsWriteAndFlush(SupportsWrite[_T_contra], Protocol[_T_contra]):
    def flush(self) -> None: ...

@overload
def print(
    *values: object,
    sep: str | None = ...,
    end: str | None = ...,
    file: SupportsWrite[str] | None = ...,
    flush: Literal[False] = ...,
) -> None: ...
@overload
def print(
    *values: object, sep: str | None = ..., end: str | None = ..., file: _SupportsWriteAndFlush[str] | None = ..., flush: bool
) -> None: ...

_E = TypeVar("_E", contravariant=True)
_M = TypeVar("_M", contravariant=True)

class _SupportsPow2(Protocol[_E, _T_co]):
    def __pow__(self, __other: _E) -> _T_co: ...

class _SupportsPow3NoneOnly(Protocol[_E, _T_co]):
    def __pow__(self, __other: _E, __modulo: None = ...) -> _T_co: ...

class _SupportsPow3(Protocol[_E, _M, _T_co]):
    def __pow__(self, __other: _E, __modulo: _M) -> _T_co: ...

_SupportsSomeKindOfPow = Union[_SupportsPow2[Any, Any], _SupportsPow3NoneOnly[Any, Any], _SupportsPow3[Any, Any, Any]]

if sys.version_info >= (3, 8):
    @overload
    def pow(base: int, exp: int, mod: Literal[0]) -> NoReturn: ...
    @overload
    def pow(base: int, exp: int, mod: int) -> int: ...
    @overload
    def pow(base: int, exp: _PositiveInteger, mod: None = ...) -> int: ...  # type: ignore[misc]
    @overload
    def pow(base: int, exp: _NegativeInteger, mod: None = ...) -> float: ...  # type: ignore[misc]
    # int base & positive-int exp -> int; int base & negative-int exp -> float
    # return type must be Any as `int | float` causes too many false-positive errors
    @overload
    def pow(base: int, exp: int, mod: None = ...) -> Any: ...
    @overload
    def pow(base: float, exp: int, mod: None = ...) -> float: ...
    # float base & float exp could return float or complex
    # return type must be Any (same as complex base, complex exp),
    # as `float | complex` causes too many false-positive errors
    @overload
    def pow(base: float, exp: complex | _SupportsSomeKindOfPow, mod: None = ...) -> Any: ...
    @overload
    def pow(base: complex, exp: complex | _SupportsSomeKindOfPow, mod: None = ...) -> complex: ...
    @overload
    def pow(base: _SupportsPow2[_E, _T_co], exp: _E, mod: None = ...) -> _T_co: ...
    @overload
    def pow(base: _SupportsPow3NoneOnly[_E, _T_co], exp: _E, mod: None = ...) -> _T_co: ...
    @overload
    def pow(base: _SupportsPow3[_E, _M, _T_co], exp: _E, mod: _M = ...) -> _T_co: ...
    @overload
    def pow(base: _SupportsSomeKindOfPow, exp: float, mod: None = ...) -> Any: ...
    @overload
    def pow(base: _SupportsSomeKindOfPow, exp: complex, mod: None = ...) -> complex: ...

else:
    @overload
    def pow(__base: int, __exp: int, __mod: Literal[0]) -> NoReturn: ...
    @overload
    def pow(__base: int, __exp: int, __mod: int) -> int: ...
    @overload
    def pow(__base: int, __exp: _PositiveInteger, __mod: None = ...) -> int: ...  # type: ignore[misc]
    @overload
    def pow(__base: int, __exp: _NegativeInteger, __mod: None = ...) -> float: ...  # type: ignore[misc]
    @overload
    def pow(__base: int, __exp: int, __mod: None = ...) -> Any: ...
    @overload
    def pow(__base: float, __exp: int, __mod: None = ...) -> float: ...
    @overload
    def pow(__base: float, __exp: complex | _SupportsSomeKindOfPow, __mod: None = ...) -> Any: ...
    @overload
    def pow(__base: complex, __exp: complex | _SupportsSomeKindOfPow, __mod: None = ...) -> complex: ...
    @overload
    def pow(__base: _SupportsPow2[_E, _T_co], __exp: _E, __mod: None = ...) -> _T_co: ...
    @overload
    def pow(__base: _SupportsPow3NoneOnly[_E, _T_co], __exp: _E, __mod: None = ...) -> _T_co: ...
    @overload
    def pow(__base: _SupportsPow3[_E, _M, _T_co], __exp: _E, __mod: _M = ...) -> _T_co: ...
    @overload
    def pow(__base: _SupportsSomeKindOfPow, __exp: float, __mod: None = ...) -> Any: ...
    @overload
    def pow(__base: _SupportsSomeKindOfPow, __exp: complex, __mod: None = ...) -> complex: ...

def quit(code: object = ...) -> NoReturn: ...

class reversed(Iterator[_T], Generic[_T]):
    @overload
    def __init__(self, __sequence: Reversible[_T]) -> None: ...
    @overload
    def __init__(self, __sequence: SupportsLenAndGetItem[_T]) -> None: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __next__(self) -> _T: ...

def repr(__obj: object) -> str: ...
@overload
def round(number: SupportsRound[Any]) -> int: ...
@overload
def round(number: SupportsRound[Any], ndigits: None) -> int: ...
@overload
def round(number: SupportsRound[_T], ndigits: SupportsIndex) -> _T: ...

# See https://github.com/python/typeshed/pull/6292#discussion_r748875189
# for why arg 3 of `setattr` should be annotated with `Any` and not `object`
def setattr(__obj: object, __name: str, __value: Any) -> None: ...
@overload
def sorted(__iterable: Iterable[SupportsRichComparisonT], *, key: None = ..., reverse: bool = ...) -> list[SupportsRichComparisonT]: ...
@overload
def sorted(__iterable: Iterable[_T], *, key: Callable[[_T], SupportsRichComparison], reverse: bool = ...) -> list[_T]: ...

if sys.version_info >= (3, 8):
    @overload
    def sum(__iterable: Iterable[_T]) -> _T | int: ...
    @overload
    def sum(__iterable: Iterable[_T], start: _S) -> _T | _S: ...

else:
    @overload
    def sum(__iterable: Iterable[_T]) -> _T | int: ...
    @overload
    def sum(__iterable: Iterable[_T], __start: _S) -> _T | _S: ...

# The argument to `vars()` has to have a `__dict__` attribute, so can't be annotated with `object`
# (A "SupportsDunderDict" protocol doesn't work)
def vars(__object: Any = ...) -> dict[str, Any]: ...

class zip(Iterator[_T_co], Generic[_T_co]):
    if sys.version_info >= (3, 10):
        @overload
        def __new__(cls, __iter1: Iterable[_T1], *, strict: bool = ...) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], *, strict: bool = ...) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(
            cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3], *, strict: bool = ...
        ) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            *,
            strict: bool = ...,
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            __iter5: Iterable[_T5],
            *,
            strict: bool = ...,
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[Any],
            __iter2: Iterable[Any],
            __iter3: Iterable[Any],
            __iter4: Iterable[Any],
            __iter5: Iterable[Any],
            __iter6: Iterable[Any],
            *iterables: Iterable[Any],
            strict: bool = ...,
        ) -> zip[Tuple[Any, ...]]: ...
    else:
        @overload
        def __new__(cls, __iter1: Iterable[_T1]) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2]) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3]) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls, __iter1: Iterable[_T1], __iter2: Iterable[_T2], __iter3: Iterable[_T3], __iter4: Iterable[_T4]
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[_T1],
            __iter2: Iterable[_T2],
            __iter3: Iterable[_T3],
            __iter4: Iterable[_T4],
            __iter5: Iterable[_T5],
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            __iter1: Iterable[Any],
            __iter2: Iterable[Any],
            __iter3: Iterable[Any],
            __iter4: Iterable[Any],
            __iter5: Iterable[Any],
            __iter6: Iterable[Any],
            *iterables: Iterable[Any],
        ) -> zip[Tuple[Any, ...]]: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __next__(self) -> _T_co: ...

# Signature of `builtins.__import__` should be kept identical to `importlib.__import__`
# Return type of `__import__` should be kept the same as return type of `importlib.import_module`
def __import__(
    name: str,
    globals: Mapping[str, object] | None = ...,
    locals: Mapping[str, object] | None = ...,
    fromlist: Sequence[str] = ...,
    level: int = ...,
) -> types.ModuleType: ...

# Actually the type of Ellipsis is <type 'ellipsis'>, but since it's
# not exposed anywhere under that name, we make it private here.
@final
class ellipsis: ...

Ellipsis: ellipsis

class BaseException(object):
    args: Tuple[Any, ...]
    __cause__: BaseException | None
    __context__: BaseException | None
    __suppress_context__: bool
    __traceback__: TracebackType | None
    def __init__(self, *args: object) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def with_traceback(self: _TBE, __tb: TracebackType | None) -> _TBE: ...

class GeneratorExit(BaseException): ...
class KeyboardInterrupt(BaseException): ...

class SystemExit(BaseException):
    code: int

class Exception(BaseException): ...

class StopIteration(Exception):
    value: Any

_StandardError = Exception

class OSError(Exception):
    errno: int
    strerror: str
    # filename, filename2 are actually str | bytes | None
    filename: Any
    filename2: Any
    if sys.platform == "win32":
        winerror: int

EnvironmentError = OSError
IOError = OSError
if sys.platform == "win32":
    WindowsError = OSError

class ArithmeticError(_StandardError): ...
class AssertionError(_StandardError): ...

class AttributeError(_StandardError):
    if sys.version_info >= (3, 10):
        name: str
        obj: object

class BufferError(_StandardError): ...
class EOFError(_StandardError): ...

class ImportError(_StandardError):
    def __init__(self, *args: object, name: str | None = ..., path: str | None = ...) -> None: ...
    name: str | None
    path: str | None
    msg: str  # undocumented

class LookupError(_StandardError): ...
class MemoryError(_StandardError): ...

class NameError(_StandardError):
    if sys.version_info >= (3, 10):
        name: str

class ReferenceError(_StandardError): ...
class RuntimeError(_StandardError): ...

class StopAsyncIteration(Exception):
    value: Any

class SyntaxError(_StandardError):
    msg: str
    lineno: int | None
    offset: int | None
    text: str | None
    filename: str | None
    if sys.version_info >= (3, 10):
        end_lineno: int | None
        end_offset: int | None

class SystemError(_StandardError): ...
class TypeError(_StandardError): ...
class ValueError(_StandardError): ...
class FloatingPointError(ArithmeticError): ...
class OverflowError(ArithmeticError): ...
class ZeroDivisionError(ArithmeticError): ...
class ModuleNotFoundError(ImportError): ...
class IndexError(LookupError): ...
class KeyError(LookupError): ...
class UnboundLocalError(NameError): ...

class BlockingIOError(OSError):
    characters_written: int

class ChildProcessError(OSError): ...
class ConnectionError(OSError): ...
class BrokenPipeError(ConnectionError): ...
class ConnectionAbortedError(ConnectionError): ...
class ConnectionRefusedError(ConnectionError): ...
class ConnectionResetError(ConnectionError): ...
class FileExistsError(OSError): ...
class FileNotFoundError(OSError): ...
class InterruptedError(OSError): ...
class IsADirectoryError(OSError): ...
class NotADirectoryError(OSError): ...
class PermissionError(OSError): ...
class ProcessLookupError(OSError): ...
class TimeoutError(OSError): ...
class NotImplementedError(RuntimeError): ...
class RecursionError(RuntimeError): ...
class IndentationError(SyntaxError): ...
class TabError(IndentationError): ...
class UnicodeError(ValueError): ...

class UnicodeDecodeError(UnicodeError):
    encoding: str
    object: bytes
    start: int
    end: int
    reason: str
    def __init__(self, __encoding: str, __object: bytes, __start: int, __end: int, __reason: str) -> None: ...

class UnicodeEncodeError(UnicodeError):
    encoding: str
    object: str
    start: int
    end: int
    reason: str
    def __init__(self, __encoding: str, __object: str, __start: int, __end: int, __reason: str) -> None: ...

class UnicodeTranslateError(UnicodeError): ...
class Warning(Exception): ...
class UserWarning(Warning): ...
class DeprecationWarning(Warning): ...
class SyntaxWarning(Warning): ...
class RuntimeWarning(Warning): ...
class FutureWarning(Warning): ...
class PendingDeprecationWarning(Warning): ...
class ImportWarning(Warning): ...
class UnicodeWarning(Warning): ...
class BytesWarning(Warning): ...
class ResourceWarning(Warning): ...

if sys.version_info >= (3, 10):
    class EncodingWarning(Warning): ...
