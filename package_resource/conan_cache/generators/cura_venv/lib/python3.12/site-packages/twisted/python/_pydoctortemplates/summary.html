<!DOCTYPE html>
<html xmlns:t="http://twistedmatrix.com/ns/twisted.web.template/0.1">
  <head>
    <title t:render="title">Something</title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="apidocs.css"/>
  </head>
  <body>
    <nav class="navbar navbar-default">
      <div class="container">

        <div class="navbar-header">
          <a class="navbar-brand" href="index.html">
            <t:transparent t:render="project">Some Project</t:transparent> API Documentation
          </a>
        </div>
      </div>
    </nav>

    <div style="display: none" id="current-docs-container" class="container">
      <div class="col-sm-12">
        <a id="current-docs-link">
          Go to the latest version of this document.
        </a>
      </div>
    </div>

    <div id="showPrivate">
      <button class="btn btn-link" onclick="togglePrivate()">Toggle Private API</button>
    </div>

    <div class="container">

      <div class="page-header">
        <h1 t:render="heading">Heading</h1>
      </div>

      <ul id="summaryTree" t:render="stuff">
      </ul>

    </div>

    <!-- Google analytics, obviously. -->
    <script src="//www.google-analytics.com/urchin.js" type="text/javascript"></script>
    <script type="text/javascript">
      _uacct = "UA-99018-6";
      urchinTracker();
    </script>

    <!-- If the documentation isn't current, insert a current link. -->
    <script type="text/javascript">
      if (window.location.pathname.indexOf('/current/') == -1) {
        <!-- Give the user a link to this page, but in the current version of the docs. -->
        var link = document.getElementById('current-docs-link');
        link.href = window.location.pathname.replace(/\/\d+\.\d+\.\d+\/api\//, '/current/api/');
        <!-- And make it visible -->
        var container = document.getElementById('current-docs-container');
        container.style.display = "";
        delete link;
        delete container;
      }
    </script>

    <script src="pydoctor.js" type='text/javascript'></script>

  </body>
</html>
