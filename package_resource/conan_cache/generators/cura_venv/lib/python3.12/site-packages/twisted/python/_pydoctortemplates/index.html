<!DOCTYPE html>
<html xmlns:t="http://twistedmatrix.com/ns/twisted.web.template/0.1">
  <head>
    <title>
      API Documentation for
      <t:transparent t:render="project">Some Project</t:transparent>
    </title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="apidocs.css" />
  </head>
  <body>

    <nav class="navbar navbar-default">
      <div class="container">
        <div class="navbar-header">
          <a class="navbar-brand" href="index.html">
            <t:transparent t:render="project">Some Project</t:transparent> API Documentation
          </a>
        </div>
      </div>
    </nav>

    <div style="display: none" id="current-docs-container" class="container">
      <div class="col-sm-12">
        <a id="current-docs-link">
          Go to the latest version of this document.
        </a>
      </div>
    </div>

    <div class="container">
      <h2>
        Get Started
      </h2>
      <ul>
        <li>
          A listing of <a href="moduleIndex.html">all modules and
          packages</a>, organized by package hierarchy.
        </li>
        <li>
          A listing of <a href="classIndex.html">all classes</a>,
          organized by inheritance hierarchy.
        </li>
        <li>
          A listing of <a href="nameIndex.html">all functions, classes,
          modules and packages</a>, ordered by name.
        </li>
        <li t:render="recentChanges">
          See <a href="recentChanges">recent changes</a> made online to
          docstrings.
        </li>
        <li t:render="problemObjects">
          See <a href="problemObjects">objects with formatting problems</a>.
        </li>
        <li t:render="onlyIfOneRoot">
          Start at <a href="root.html">root</a>, the root package.
        </li>
        <t:transparent t:render="onlyIfMultipleRoots">
          <li>
            Or start at one of the root <t:transparent t:render="rootkind">
            packages</t:transparent>:
            <ul>
              <li t:render="roots">
                <t:slot name="root"/>
              </li>
            </ul>
          </li>
        </t:transparent>
      </ul>
      <h2>
        About
      </h2>
      <p>
        <address>
          <a href="index.html">API Documentation</a> for <t:transparent t:render="project_link">Some
            Project</t:transparent>, generated by
            <a href="https://github.com/twisted/pydoctor/">pydoctor</a>
            <t:transparent t:render="version" />
            at <t:transparent t:render="buildtime">some time</t:transparent>.
        </address>
      </p>

    </div>

    <!-- Google analytics, obviously. -->
    <script src="//www.google-analytics.com/urchin.js" type="text/javascript"></script>
    <script type="text/javascript">
      _uacct = "UA-99018-6";
      urchinTracker();
    </script>

    <!-- If the documentation isn't current, insert a current link. -->
    <script type="text/javascript">
      if (window.location.pathname.indexOf('/current/') == -1) {
        <!-- Give the user a link to this page, but in the current version of the docs. -->
        var link = document.getElementById('current-docs-link');
        link.href = window.location.pathname.replace(/\/\d+\.\d+\.\d+\/api\//, '/current/api/');
        <!-- And make it visible -->
        var container = document.getElementById('current-docs-container');
        container.style.display = "";
        delete link;
        delete container;
      }
    </script>

  </body>
</html>
