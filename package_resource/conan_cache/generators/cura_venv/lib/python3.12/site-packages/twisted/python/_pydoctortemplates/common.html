<!DOCTYPE html>
<html xmlns:t="http://twistedmatrix.com/ns/twisted.web.template/0.1" t:render="all">
  <head>
    <title><t:slot name="title">Something</t:slot> : API documentation</title>

    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="apidocs.css"/>
  </head>
  <body>

    <nav class="navbar navbar-default">
      <div class="container">
        <div class="navbar-header">
          <a class="navbar-brand" href="index.html">
            Twisted API Documentation
          </a>
        </div>
      </div>
    </nav>

   <div style="display: none" id="current-docs-container" class="container">
      <div class="col-sm-12">
        <a id="current-docs-link">
          Go to the latest version of this document.
        </a>
      </div>
    </div>

    <div id="showPrivate">
      <button class="btn btn-link" onclick="togglePrivate()">Toggle Private API</button>
    </div>

    <div class="container">

      <div class="page-header">
        <t:slot name="heading"><h1>Heading</h1></t:slot>
      </div>

      <div class="categoryHeader">
        <t:slot name="category">something documentation</t:slot>
      </div>

      <div class="extrasDocstring">
        <t:slot name="extras">
          Inheritance info.
        </t:slot>
        <p><a t:render="inhierarchy">View In Hierarchy</a></p>
      </div>

      <t:transparent t:render="deprecated" />

      <div class="moduleDocstring">
        <t:slot name="docstring">
          A docstring.
        </t:slot>
      </div>

      <div id="splitTables">
        <t:slot name="mainTable" />
        <t:transparent t:render="baseTables">
          <p class="inheritedFrom">
            Inherited from <t:slot name="baseName" />:
          </p>
          <t:slot name="baseTable" />
          </t:transparent>

          <t:slot name="packageInitTable"> </t:slot>
      </div>

      <div id="childList">

        <t:slot name="childlist"> </t:slot>

      </div>
      <address>
        <a href="index.html">API Documentation</a> for <t:slot name="project">Some
          Project</t:slot>, generated by <a href="https://github.com/twisted/pydoctor/">pydoctor</a> <t:slot name="version" /> at <t:slot name="buildtime">some time</t:slot>.
      </address>

    </div>

    <script src="pydoctor.js" type='text/javascript'></script>

    <!-- Google analytics, obviously. -->
    <script src="//www.google-analytics.com/urchin.js" type="text/javascript"></script>
    <script type="text/javascript">
      _uacct = "UA-99018-6";
      urchinTracker();
    </script>

    <!-- If the documentation isn't current, insert a current link. -->
    <script type="text/javascript">
      if (window.location.pathname.indexOf('/current/') == -1) {
        <!-- Give the user a link to this page, but in the current version of the docs. -->
        var link = document.getElementById('current-docs-link');
        link.href = window.location.pathname.replace(/\/\d+\.\d+\.\d+\/api\//, '/current/api/');
        <!-- And make it visible -->
        var container = document.getElementById('current-docs-container');
        container.style.display = "";
        delete link;
        delete container;
      }
    </script>

  </body>
</html>
